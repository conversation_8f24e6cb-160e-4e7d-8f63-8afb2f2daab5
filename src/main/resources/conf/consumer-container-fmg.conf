// Extra configuration for stream protocol consumer container.
// Main configuration is set programatically in StreamProtocolMarketStreamConsumerSupplier class
consumer-container {
  name = "ConsumerContainer-FMG"
  consumer {
    reactive-consumer {
      # incrementing this parallelism will lead to unordered commits to kafka,
      # but the order of messages per entity will be preserved
      parallelism = 1
      # the strategy to be used in case of failure during instruction processing
      on-failure-strategy = "com.ppb.platform.stream.consumer.core.consumer.reactive.strategy.SkipFailedRecordStrategy"
    }
    partition-consumer {
      shard.number = 50
    }
  }
  bootstrap {
    init-timeout = 120 seconds
    metadata-consumer {
      # The modes available:
      # latest (Default): This mode starts the consumer from the latest snapshot available.
      # at-least-once: This mode start the consumer from the last snapshot per partition from the offset where the consumer stops based on its commit, or if the consumer is newer from the latest snapshot.
      #    NOTE: This feature is limited to the number of snapshots kept by the producer and when no snapshot prior to the committed offset was found on metadata service the consumer will fallback from the oldest snapshot.
      mode = latest
    }
    http {
      port = 8092
    }
  }
  //  tracing {
  //    name = "FMG"
  //    jaeger {
  //      hostname = "kafka"
  //      port = 6831
  //      flush-interval-ms = 1000
  //      max-queue-size = 100
  //      samplerType = "remote"
  //      samplerParam = 0.1
  //      logSpan = false
  //      metrics {
  //        enabled = true
  //      }
  //    }
  //  }
}

jmx {
  base.name = "com.ppb.platform.stream.protocol.consumerContainerFMG"
}