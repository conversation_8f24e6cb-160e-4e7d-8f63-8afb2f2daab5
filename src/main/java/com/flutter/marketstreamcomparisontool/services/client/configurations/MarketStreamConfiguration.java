package com.flutter.marketstreamcomparisontool.services.client.configurations;

import com.betfair.mantis.performance.ClassBasedPredicate;
import com.betfair.mantis.performance.PerformanceDisruptor;
import com.betfair.mantis.performance.ServiceDataEventHandler;
import com.betfair.platform.fms.model.MarketChange;
import com.betfair.platform.fms.model.MarketView;
import com.flutter.marketstreamcomparisontool.services.client.notifiers.TurnInplayNotifier;
import com.google.common.eventbus.EventBus;
import com.ppb.platform.sb.fmg.MarketStreamClient;
import com.ppb.platform.sb.metrics.mantis.MantisMonitorDispatcher;
import com.ppb.platform.sb.toggle.feature.FeatureToggle;
import com.ppb.platform.sb.toggle.feature.SimpleFeatureToggle;
import com.ppb.platform.stream.consumer.core.notifier.StreamNotificationObserver;
import com.typesafe.config.ConfigFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.Lazy;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.betfair.mantis.performance.PerformanceEvent.PerfEventClass.SERVICE;


@Configuration
/* Needed to import a performanceDisruptor bean required by the MarketStreamClient */
@ImportResource("classpath:performance-logging-context-common.xml")
/* Uncomment if you prefer not to have the tracing* properties coming from dependency in application.properties */
//@PropertySource({"classpath:application.properties", "classpath:performance.properties"})
@SuppressWarnings("UnstableApiUsage")
public class MarketStreamConfiguration {

    @Autowired
    @Lazy
    PerformanceDisruptor performanceDisruptor;

    @Bean
    public MarketStreamClient marketStreamClientGSSP(@Value("${msct.market.stream.client.config.gssp}") final String marketStreamClientConfigGSSP,
                                                     @Value("${msct.market.stream.consumer.config.gssp}") final String marketStreamConsumerConfigGSSP,
                                                     @Value("${msct.market.stream.consumer.container.config.gssp}") final String marketStreamConsumerContainerConfigGSSP) throws Exception {

        String clientName = ConfigFactory.load(marketStreamClientConfigGSSP).getString("fms.clientName");
        return new MarketStreamClient.MarketStreamClientBuilder(marketStreamClientConfigGSSP)
                .setProvidedConsumerConfig(marketStreamConsumerConfigGSSP)
                .setProvidedConsumerContainerConfig(marketStreamConsumerContainerConfigGSSP)
                .setMonitorDispatcher(mantisMonitorDispatcherGSSP(performanceDisruptor))
                .setProvidedObserverList(notificationObserverList(clientName, marketChangeEventBus()))
                .build();
    }

    @Bean
    public MarketStreamClient marketStreamClientFMG(@Value("${msct.market.stream.client.config.fmg}") final String marketStreamClientConfigFMG,
                                                    @Value("${msct.market.stream.consumer.config.fmg}") final String marketStreamConsumerConfigFMG,
                                                    @Value("${msct.market.stream.consumer.container.config.fmg}") final String marketStreamConsumerContainerConfigFMG) throws Exception {
        String clientName = ConfigFactory.load(marketStreamClientConfigFMG).getString("fms.clientName");
        return new MarketStreamClient.MarketStreamClientBuilder(marketStreamClientConfigFMG)
                .setProvidedConsumerConfig(marketStreamConsumerConfigFMG)
                .setProvidedConsumerContainerConfig(marketStreamConsumerContainerConfigFMG)
                .setMonitorDispatcher(mantisMonitorDispatcherFMG(performanceDisruptor))
                .setProvidedObserverList(notificationObserverList(clientName, marketChangeEventBus()))
                .build();
    }


    @Bean
    public FeatureToggle marketStreamClientToggleGSSP(@Value("${msct.market.stream.client.enabled.gssp}") final boolean marketStreamClientEnabledGSSP) {
        return new SimpleFeatureToggle(marketStreamClientEnabledGSSP, "marketStreamClientGSSP");
    }

    @Bean
    public FeatureToggle marketStreamClientToggleFMG(@Value("${msct.market.stream.client.enabled.fmg}") final boolean marketStreamClientEnabledFMG) {
        return new SimpleFeatureToggle(marketStreamClientEnabledFMG, "marketStreamClientFMG");
    }

    @Bean
    public MantisMonitorDispatcher mantisMonitorDispatcherGSSP(PerformanceDisruptor performanceDisruptor){
        return new MantisMonitorDispatcher(performanceDisruptor);
    }

    @Bean
    public MantisMonitorDispatcher mantisMonitorDispatcherFMG(PerformanceDisruptor performanceDisruptor){
        return new MantisMonitorDispatcher(performanceDisruptor);
    }

    @Bean
    public EventBus marketChangeEventBus() {
        return new EventBus();
    }

    @Bean
    public Set<Class<?>> metricTags() {
        return new HashSet<>();
    }

    @Bean
    public Set<ServiceDataEventHandler> performanceEventHandlers(ServiceDataEventHandler services,
                                                                 @Value("${tracing.stats.age}") int maxAge,
                                                                 @Value("${tracing.stats.size}") int maxSize) {
        ServiceDataEventHandler handler = new ServiceDataEventHandler(new ClassBasedPredicate(SERVICE));
        handler.setMaxAge(maxAge);
        handler.setMaxSize(maxSize);

        Set<ServiceDataEventHandler> set = new HashSet<>();
        set.add(services);
        return set;
    }

    public List<StreamNotificationObserver<MarketView, MarketChange>> notificationObserverList(String clientName, EventBus eventBus) {
        TurnInplayNotifier turnInplayNotifier = new TurnInplayNotifier(clientName, eventBus);
        return List.of(turnInplayNotifier);
    }
}
