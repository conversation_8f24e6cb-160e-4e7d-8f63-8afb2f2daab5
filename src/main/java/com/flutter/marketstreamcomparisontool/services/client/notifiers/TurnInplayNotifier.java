package com.flutter.marketstreamcomparisontool.services.client.notifiers;

import com.betfair.platform.fms.model.MarketChange;
import com.betfair.platform.fms.model.MarketView;
import com.flutter.marketstreamcomparisontool.services.comparator.notification.Trigger;
import com.google.common.eventbus.EventBus;
import com.ppb.platform.stream.consumer.core.notifier.StreamNotificationObserver;
import com.ppb.platform.stream.consumer.datasource.State;
import com.ppb.platform.stream.consumer.notifier.NotificationType;
import com.ppb.platform.stream.model.context.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import scala.Option;

@SuppressWarnings("UnstableApiUsage")
@Slf4j
public class TurnInplayNotifier implements StreamNotificationObserver<MarketView, MarketChange> {

    private final EventBus eventBus;
    private final String name;

    public TurnInplayNotifier(String name, EventBus eventBus) {
        log.info("operation='Start Listener', msg='TurnInplayListener initialized', name={}", name);
        this.name = name;
        this.eventBus = eventBus;
    }

    @Override
    public void onNotification(
            NotificationType notificationType,
            String streamId,
            State state,
            MarketChange change,
            ContextHolder protocolCtx,
            Option<MarketView> oldData,
            MarketView newData) {


        boolean wasInplay = oldData.isDefined() && oldData.get().getMarketDefinition().getMarket().isInplay();
        boolean isInplay = newData.getMarketDefinition().getMarket().isInplay();

        if (!wasInplay && isInplay) {
            log.debug("operation='Inplay Check', name={}, wasInplay={}, isInplay={}, marketId={}",
                    this.name, wasInplay, isInplay, newData.getMarketId());
            eventBus.post(new Trigger(newData.getMarketId(), Trigger.Type.TURN_INPLAY, this.name, newData));
        }
    }
}
