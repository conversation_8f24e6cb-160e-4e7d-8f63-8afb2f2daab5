package com.flutter.marketstreamcomparisontool.services.comparator;

import com.flutter.marketstreamcomparisontool.services.comparator.notification.Trigger;
import com.google.common.eventbus.EventBus;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jmx.export.annotation.ManagedOperation;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@SuppressWarnings("UnstableApiUsage")
@ManagedResource(objectName = "com.flutter.marketstreamcomparisontool.services.comparator:name=Comparator")
@Service
@Slf4j
public class Comparator {

    private final Map<String, Trigger> persistence = new ConcurrentHashMap<>();

    Comparator(EventBus eventBus) {
        eventBus.register(this);
    }

    @Subscribe
    public void apply(Trigger current) {
        String canonicalKey = current.getEntityId() + "-" + current.getSignal();

        if (persistence.containsKey(canonicalKey)) {
           Trigger previous = persistence.get(canonicalKey);
           if (previous.getOrigin().equals(canonicalKey)) {
               log.warn("operation='Duplicate Trigger', msg='Ignoring previous trigger and store current as is same origin', current={}", current);
               persistence.put(canonicalKey, current);
           } else {
               compareAndNotify(previous, current);
               persistence.remove(canonicalKey);
           }
        } else {
            persistence.put(canonicalKey, current);
        }

    }

    private void compareAndNotify(Trigger previous, Trigger current) {
        if (previous.getState().equals(current.getState())) {
            log.debug("operation='No Change Detected', msg='Both triggers have same state, ignoring', previous={}, current={}", previous, current);
        } else {
            log.error("operation='Change Detected', msg='Triggers have different states, notifying', previous={}, current={}", previous, current);
            // Here you would add the logic to notify about the change
        }
    }

    @ManagedOperation(description = "Get the state of an entity")
    public Trigger getState(String entityId) {
        return persistence.get(entityId);
    }
}
