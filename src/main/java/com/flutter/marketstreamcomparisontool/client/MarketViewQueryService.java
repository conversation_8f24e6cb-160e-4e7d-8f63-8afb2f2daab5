package com.flutter.marketstreamcomparisontool.client;

import com.betfair.platform.fms.model.MarketView;
import com.ppb.platform.sb.fmg.MarketStreamClient;
import com.ppb.platform.sb.toggle.feature.FeatureToggle;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

import static com.google.common.base.Preconditions.checkArgument;

@Component
public class MarketViewQueryService {

    private final MarketStreamClient marketStreamClientGSSP;
    private final MarketStreamClient marketStreamClientFMG;

    public MarketViewQueryService(
            MarketStreamClient marketStreamClientGSSP,
            MarketStreamClient marketStreamClientFMG) {
        checkArgument(marketStreamClientGSSP != null, "marketStreamClientGSSP is mandatory");
        checkArgument(marketStreamClientFMG != null, "marketStreamClientFMG is mandatory");
        this.marketStreamClientGSSP = marketStreamClientGSSP;
        this.marketStreamClientFMG = marketStreamClientFMG;
    }

    public MarketView fetchByMarketId(String marketId, String source) {
        return Objects.equals(source, "gssp") ? marketStreamClientGSSP.get(marketId) : marketStreamClientFMG.get(marketId);
    }

}
